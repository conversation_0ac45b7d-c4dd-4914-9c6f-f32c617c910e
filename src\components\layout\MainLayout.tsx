
/**
 * Main Application Layout
 * Provides the primary structure for the application
 */

import { ReactNode, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuthStore } from '@/store/auth-store';
import { useAppStore } from '@/store/app-store';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  Settings, 
  Moon, 
  Sun, 
  Monitor,
  Menu,
  X,
  Languages
} from 'lucide-react';

interface MainLayoutProps {
  children: ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const { user, isAuthenticated, logout } = useAuthStore();
  const { 
    sidebarOpen, 
    toggleSidebar, 
    settings, 
    setTheme, 
    setLanguage 
  } = useAppStore();

  const themeIcons = {
    light: Sun,
    dark: Moon,
    system: Monitor,
  };

  const ThemeIcon = themeIcons[settings.theme];

  const cycleTheme = () => {
    const themes: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system'];
    const currentIndex = themes.indexOf(settings.theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];
    setTheme(nextTheme);
  };

  const toggleLanguage = () => {
    setLanguage(settings.language === 'en' ? 'ar' : 'en');
  };

  return (
    <div 
      className={`min-h-screen bg-background text-foreground transition-all duration-300 ${
        settings.rtl ? 'font-arabic' : ''
      }`}
      dir={settings.rtl ? 'rtl' : 'ltr'}
    >
      {/* Header */}
      <header className="h-16 border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="h-full px-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="hover:bg-accent"
            >
              <Menu className="h-5 w-5" />
            </Button>
            
            <div className="flex items-center gap-2">
              <FileText className="h-6 w-6 text-ai-primary-600" />
              <h1 className="text-xl font-semibold ai-gradient-text">
                BuildDocWithAI
              </h1>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleLanguage}
              className="hover:bg-accent"
              title={settings.language === 'en' ? 'Switch to Arabic' : 'التبديل إلى الإنجليزية'}
            >
              <Languages className="h-5 w-5" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              onClick={cycleTheme}
              className="hover:bg-accent"
            >
              <ThemeIcon className="h-5 w-5" />
            </Button>

            {isAuthenticated() && (
              <>
                <Button variant="ghost" size="icon">
                  <Settings className="h-5 w-5" />
                </Button>
                
                <div className="flex items-center gap-2 ml-4">
                  <span className="text-sm text-muted-foreground">
                    {user?.email}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={logout}
                  >
                    {settings.language === 'ar' ? 'تسجيل الخروج' : 'Logout'}
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sidebar */}
        <AnimatePresence mode="wait">
          {sidebarOpen && (
            <motion.aside
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 280, opacity: 1 }}
              exit={{ width: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="border-r bg-card/30 backdrop-blur-sm overflow-hidden"
            >
              <div className="p-4 h-full overflow-y-auto">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="font-semibold text-lg">
                    {settings.language === 'ar' ? 'المستندات' : 'Documents'}
                  </h2>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleSidebar}
                    className="hover:bg-accent"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Sidebar content will be added in future components */}
                <div className="space-y-2">
                  <div className="p-3 rounded-lg border border-dashed border-muted-foreground/30 text-center text-sm text-muted-foreground">
                    {settings.language === 'ar' 
                      ? 'لا توجد مستندات بعد' 
                      : 'No documents yet'
                    }
                  </div>
                </div>
              </div>
            </motion.aside>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <main className="flex-1 overflow-hidden">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>
      </div>
    </div>
  );
}

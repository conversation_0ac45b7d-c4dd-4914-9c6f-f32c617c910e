
/**
 * PDF Generation Service
 * Handles document export to PDF format using multiple libraries
 */

import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export interface PDFGenerationOptions {
  title?: string;
  author?: string;
  subject?: string;
  format?: 'a4' | 'letter' | 'legal';
  orientation?: 'portrait' | 'landscape';
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  includeHeader?: boolean;
  includeFooter?: boolean;
  watermark?: string;
}

export interface PDFExportResult {
  success: boolean;
  blob?: Blob;
  error?: string;
}

class PDFGeneratorService {
  private defaultOptions: PDFGenerationOptions = {
    format: 'a4',
    orientation: 'portrait',
    margins: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20,
    },
    includeHeader: true,
    includeFooter: true,
  };

  async generateFromMarkdown(
    markdownContent: string,
    options: PDFGenerationOptions = {}
  ): Promise<PDFExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      const doc = new jsPDF({
        orientation: mergedOptions.orientation,
        unit: 'mm',
        format: mergedOptions.format,
      });

      // Convert markdown to plain text for basic PDF generation
      const plainText = this.markdownToPlainText(markdownContent);
      
      // Set document properties
      if (mergedOptions.title) doc.setTitle(mergedOptions.title);
      if (mergedOptions.author) doc.setAuthor(mergedOptions.author);
      if (mergedOptions.subject) doc.setSubject(mergedOptions.subject);

      // Add content
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();
      const margins = mergedOptions.margins!;
      
      const maxWidth = pageWidth - margins.left - margins.right;
      let yPosition = margins.top;

      // Add header
      if (mergedOptions.includeHeader && mergedOptions.title) {
        doc.setFontSize(16);
        doc.setFont(undefined, 'bold');
        doc.text(mergedOptions.title, margins.left, yPosition);
        yPosition += 15;
        
        // Add separator line
        doc.setLineWidth(0.5);
        doc.line(margins.left, yPosition, pageWidth - margins.right, yPosition);
        yPosition += 10;
      }

      // Add content
      doc.setFontSize(11);
      doc.setFont(undefined, 'normal');
      
      const lines = doc.splitTextToSize(plainText, maxWidth);
      
      for (const line of lines) {
        if (yPosition > pageHeight - margins.bottom - 10) {
          doc.addPage();
          yPosition = margins.top;
        }
        
        doc.text(line, margins.left, yPosition);
        yPosition += 6;
      }

      // Add footer
      if (mergedOptions.includeFooter) {
        const totalPages = doc.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++) {
          doc.setPage(i);
          doc.setFontSize(9);
          doc.text(
            `Page ${i} of ${totalPages}`,
            pageWidth - margins.right,
            pageHeight - margins.bottom,
            { align: 'right' }
          );
        }
      }

      // Add watermark if specified
      if (mergedOptions.watermark) {
        const totalPages = doc.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++) {
          doc.setPage(i);
          doc.setFontSize(50);
          doc.setTextColor(200, 200, 200);
          doc.text(
            mergedOptions.watermark,
            pageWidth / 2,
            pageHeight / 2,
            { 
              align: 'center',
              angle: 45,
            }
          );
        }
      }

      const blob = doc.output('blob');
      
      return {
        success: true,
        blob,
      };
    } catch (error) {
      console.error('PDF generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'PDF generation failed',
      };
    }
  }

  async generateFromHTML(
    htmlContent: string,
    options: PDFGenerationOptions = {}
  ): Promise<PDFExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      
      // Create a temporary div to render HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      tempDiv.style.cssText = `
        width: 794px;
        padding: 40px;
        font-family: Arial, sans-serif;
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        background: white;
        position: absolute;
        top: -9999px;
        left: -9999px;
      `;
      
      document.body.appendChild(tempDiv);

      // Convert HTML to canvas
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
      });

      document.body.removeChild(tempDiv);

      // Create PDF from canvas
      const doc = new jsPDF({
        orientation: mergedOptions.orientation,
        unit: 'mm',
        format: mergedOptions.format,
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = doc.internal.pageSize.getWidth();
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      doc.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      const blob = doc.output('blob');
      
      return {
        success: true,
        blob,
      };
    } catch (error) {
      console.error('HTML to PDF generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'HTML to PDF generation failed',
      };
    }
  }

  async generateFromElement(
    element: HTMLElement,
    options: PDFGenerationOptions = {}
  ): Promise<PDFExportResult> {
    try {
      const mergedOptions = { ...this.defaultOptions, ...options };
      
      // Convert element to canvas
      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
      });

      // Create PDF from canvas
      const doc = new jsPDF({
        orientation: mergedOptions.orientation,
        unit: 'mm',
        format: mergedOptions.format,
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = doc.internal.pageSize.getWidth();
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      // Handle multi-page content
      const pageHeight = doc.internal.pageSize.getHeight();
      let heightLeft = imgHeight;
      let position = 0;

      doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        doc.addPage();
        doc.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      const blob = doc.output('blob');
      
      return {
        success: true,
        blob,
      };
    } catch (error) {
      console.error('Element to PDF generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Element to PDF generation failed',
      };
    }
  }

  downloadPDF(blob: Blob, filename: string = 'document.pdf') {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  private markdownToPlainText(markdown: string): string {
    return markdown
      .replace(/#{1,6}\s+/g, '') // Remove headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/`(.*?)`/g, '$1') // Remove inline code
      .replace(/```[\s\S]*?```/g, '[Code Block]') // Replace code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links
      .replace(/!\[([^\]]*)\]\([^)]+\)/g, '[Image: $1]') // Replace images
      .replace(/^\s*[-*+]\s+/gm, '• ') // Convert lists
      .replace(/^\s*\d+\.\s+/gm, '1. ') // Convert numbered lists
      .replace(/\n{3,}/g, '\n\n'); // Normalize line breaks
  }
}

export const pdfGenerator = new PDFGeneratorService();

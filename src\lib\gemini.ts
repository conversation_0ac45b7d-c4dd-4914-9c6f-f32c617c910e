
/**
 * Gemini AI API Integration
 * Handles all AI-powered documentation generation
 */

export interface GeminiConfig {
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface DocumentGenerationRequest {
  type: 'technical' | 'user' | 'api' | 'tutorial';
  content: string;
  language?: 'en' | 'ar';
  format?: 'markdown' | 'html' | 'plain';
  template?: string;
}

export interface DocumentGenerationResponse {
  success: boolean;
  content?: string;
  error?: string;
  metadata?: {
    wordCount: number;
    estimatedReadTime: number;
    language: string;
  };
}

class GeminiService {
  private apiKey: string | null = null;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
  private defaultModel = 'gemini-1.5-flash';

  constructor() {
    // API key will be set dynamically
  }

  setApiKey(apiKey: string) {
    this.apiKey = apiKey;
  }

  private validateApiKey(): boolean {
    return !!this.apiKey;
  }

  async generateDocument(request: DocumentGenerationRequest): Promise<DocumentGenerationResponse> {
    if (!this.validateApiKey()) {
      return {
        success: false,
        error: 'Gemini API key not configured. Please set your API key in settings.',
      };
    }

    try {
      const prompt = this.buildPrompt(request);
      
      const response = await fetch(
        `${this.baseUrl}/models/${this.defaultModel}:generateContent?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: 0.7,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 2048,
            },
            safetySettings: [
              {
                category: 'HARM_CATEGORY_HARASSMENT',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE'
              },
              {
                category: 'HARM_CATEGORY_HATE_SPEECH',
                threshold: 'BLOCK_MEDIUM_AND_ABOVE'
              }
            ]
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.candidates || data.candidates.length === 0) {
        throw new Error('No content generated by Gemini API');
      }

      const generatedContent = data.candidates[0].content.parts[0].text;
      
      return {
        success: true,
        content: generatedContent,
        metadata: {
          wordCount: generatedContent.split(' ').length,
          estimatedReadTime: Math.ceil(generatedContent.split(' ').length / 200),
          language: request.language || 'en',
        },
      };
    } catch (error) {
      console.error('Gemini API error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  private buildPrompt(request: DocumentGenerationRequest): string {
    const { type, content, language = 'en', format = 'markdown' } = request;
    
    const languageInstruction = language === 'ar' 
      ? 'Please respond in Arabic (العربية) with proper RTL formatting.'
      : 'Please respond in English.';

    const formatInstruction = {
      markdown: 'Format the response using Markdown syntax with proper headers, lists, and code blocks.',
      html: 'Format the response using clean HTML with semantic tags.',
      plain: 'Format the response as plain text with clear structure.',
    }[format];

    const typeInstructions = {
      technical: 'Create comprehensive technical documentation that includes implementation details, code examples, and best practices.',
      user: 'Create user-friendly documentation that is easy to understand for non-technical users, with step-by-step instructions.',
      api: 'Create detailed API documentation including endpoints, parameters, request/response examples, and error codes.',
      tutorial: 'Create a step-by-step tutorial with clear explanations, examples, and practical exercises.',
    }[type];

    return `
${languageInstruction}

Task: Generate ${type} documentation based on the following content.

Instructions:
- ${typeInstructions}
- ${formatInstruction}
- Ensure the documentation is comprehensive, well-structured, and professional.
- Include relevant sections like Overview, Features, Usage, Examples, and Troubleshooting as appropriate.
- For technical content, include code examples and implementation notes.
- For user documentation, focus on clarity and step-by-step guidance.

Content to document:
${content}

Please generate comprehensive documentation following the above guidelines.
    `.trim();
  }

  async enhanceContent(content: string, enhancement: 'grammar' | 'clarity' | 'technical' | 'translation'): Promise<DocumentGenerationResponse> {
    if (!this.validateApiKey()) {
      return {
        success: false,
        error: 'Gemini API key not configured.',
      };
    }

    const enhancementPrompts = {
      grammar: 'Please review and correct any grammar, spelling, and punctuation errors in the following text while maintaining its original meaning and tone:',
      clarity: 'Please improve the clarity and readability of the following text, making it more concise and easier to understand:',
      technical: 'Please enhance the following text by adding more technical depth, examples, and implementation details:',
      translation: 'Please translate the following text to Arabic (العربية) while maintaining technical accuracy and proper formatting:',
    };

    const prompt = `${enhancementPrompts[enhancement]}\n\n${content}`;

    try {
      const response = await fetch(
        `${this.baseUrl}/models/${this.defaultModel}:generateContent?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: prompt
              }]
            }],
            generationConfig: {
              temperature: 0.3,
              maxOutputTokens: 2048,
            },
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.status}`);
      }

      const data = await response.json();
      const enhancedContent = data.candidates[0].content.parts[0].text;
      
      return {
        success: true,
        content: enhancedContent,
        metadata: {
          wordCount: enhancedContent.split(' ').length,
          estimatedReadTime: Math.ceil(enhancedContent.split(' ').length / 200),
          language: enhancement === 'translation' ? 'ar' : 'en',
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Enhancement failed',
      };
    }
  }
}

export const geminiService = new GeminiService();

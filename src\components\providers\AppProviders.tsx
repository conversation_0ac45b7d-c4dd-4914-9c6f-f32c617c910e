
/**
 * Main Application Providers
 * Wraps the app with all necessary providers
 */

import { ReactNode, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';
import { ToasterProvider } from './ToasterProvider';
import { useAuthStore } from '@/store/auth-store';
import { useAppStore } from '@/store/app-store';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: (failureCount, error: any) => {
        if (error?.status === 404) return false;
        return failureCount < 3;
      },
    },
  },
});

interface AppProvidersProps {
  children: ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  const { initialize } = useAuthStore();
  const { settings, setTheme, setLanguage } = useAppStore();

  useEffect(() => {
    // Initialize authentication
    initialize();
    
    // Apply initial theme and language settings
    setTheme(settings.theme);
    setLanguage(settings.language);
  }, [initialize, setTheme, setLanguage, settings.theme, settings.language]);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        {children}
        <ToasterProvider />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

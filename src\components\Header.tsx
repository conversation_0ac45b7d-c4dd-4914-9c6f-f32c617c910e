/**
 * Landing Page Header Component
 * Professional header for BuildDocwithai landing page
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { useAppStore } from '@/store/app-store';
import { 
  FileText, 
  Languages,
  Menu,
  X,
  ChevronDown
} from 'lucide-react';

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { settings, setLanguage } = useAppStore();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleLanguage = () => {
    setLanguage(settings.language === 'en' ? 'ar' : 'en');
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false);
  };

  const navigationItems = [
    {
      label: settings.language === 'ar' ? 'القوالب' : 'Templates',
      action: () => scrollToSection('templates'),
    },
    {
      label: settings.language === 'ar' ? 'كيف يعمل' : 'How it Works',
      action: () => scrollToSection('how-it-works'),
    },
    {
      label: settings.language === 'ar' ? 'الأسعار' : 'Pricing',
      action: () => scrollToSection('pricing'),
    },
  ];

  return (
    <motion.header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'bg-background/80 backdrop-blur-md border-b shadow-sm' 
          : 'bg-transparent'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <motion.div 
            className="flex items-center gap-3"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <div className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br from-saudi-emerald-500 to-saudi-blue-600 rounded-xl flex items-center justify-center">
              <FileText className="w-5 h-5 md:w-6 md:h-6 text-white" />
            </div>
            <h1 className="text-xl md:text-2xl font-bold ai-gradient-text font-vazirmatn">
              {settings.language === 'ar' ? 'بناء الوثائق' : 'BuildDocwithai'}
            </h1>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-8">
            {navigationItems.map((item, index) => (
              <motion.button
                key={index}
                onClick={item.action}
                className="text-foreground hover:text-saudi-blue-600 transition-colors font-medium"
                whileHover={{ y: -2 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                {item.label}
              </motion.button>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleLanguage}
              className="hover:bg-saudi-emerald-50 hover:text-saudi-emerald-700"
            >
              <Languages className="w-4 h-4 mr-2" />
              {settings.language === 'ar' ? 'EN' : 'عربي'}
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              className="hover:bg-saudi-blue-50 hover:border-saudi-blue-300"
            >
              {settings.language === 'ar' ? 'تسجيل الدخول' : 'Login'}
            </Button>
            
            <Button 
              size="sm"
              className="ai-gradient-bg text-white hover:scale-105 transition-transform"
            >
              {settings.language === 'ar' ? 'ابدأ مجاناً' : 'Get Started'}
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleLanguage}
              className="hover:bg-saudi-emerald-50"
            >
              <Languages className="w-4 h-4" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="hover:bg-accent"
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden border-t bg-background/95 backdrop-blur-md"
          >
            <div className="py-4 space-y-4">
              {navigationItems.map((item, index) => (
                <button
                  key={index}
                  onClick={item.action}
                  className="block w-full text-left px-4 py-2 text-foreground hover:bg-accent rounded-lg transition-colors"
                >
                  {item.label}
                </button>
              ))}
              
              <div className="px-4 pt-4 border-t space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {settings.language === 'ar' ? 'تسجيل الدخول' : 'Login'}
                </Button>
                
                <Button 
                  className="w-full ai-gradient-bg text-white"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {settings.language === 'ar' ? 'ابدأ مجاناً' : 'Get Started'}
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.header>
  );
}

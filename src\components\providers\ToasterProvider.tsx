
/**
 * Toast Notification Provider
 * Centralized notification system using Sonner
 */

import { Toaster } from "@/components/ui/sonner";
import { useAppStore } from "@/store/app-store";

export function ToasterProvider() {
  const { settings } = useAppStore();
  
  return (
    <Toaster
      position={settings.rtl ? "bottom-left" : "bottom-right"}
      theme={settings.theme === 'system' ? undefined : settings.theme}
      richColors
      closeButton
      duration={4000}
      toastOptions={{
        className: settings.rtl ? 'font-arabic' : '',
        style: {
          direction: settings.rtl ? 'rtl' : 'ltr',
        },
      }}
    />
  );
}

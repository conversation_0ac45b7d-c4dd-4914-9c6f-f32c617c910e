
/**
 * Home Page Component
 * Landing page for the AI Documentation Platform
 */

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthStore } from '@/store/auth-store';
import { useAppStore } from '@/store/app-store';
import { 
  FileText, 
  Sparkles, 
  Download, 
  Globe, 
  Zap,
  ArrowRight,
  BookOpen,
  Settings,
  Users
} from 'lucide-react';

const Index = () => {
  const { isAuthenticated } = useAuthStore();
  const { settings } = useAppStore();

  const features = [
    {
      icon: Sparkles,
      title: settings.language === 'ar' ? 'ذكاء اصطناعي متقدم' : 'AI-Powered Generation',
      description: settings.language === 'ar' 
        ? 'اكتب مستندات احترافية بواسطة الذكاء الاصطناعي من جميني'
        : 'Generate professional documentation using Gemini AI technology',
    },
    {
      icon: Globe,
      title: settings.language === 'ar' ? 'دعم متعدد اللغات' : 'Multi-Language Support',
      description: settings.language === 'ar'
        ? 'دعم كامل للغة العربية والإنجليزية مع واجهة RTL'
        : 'Full Arabic and English support with RTL interface',
    },
    {
      icon: Download,
      title: settings.language === 'ar' ? 'تصدير PDF' : 'PDF Export',
      description: settings.language === 'ar'
        ? 'قم بتصدير مستنداتك إلى ملفات PDF عالية الجودة'
        : 'Export your documents to high-quality PDF files',
    },
    {
      icon: Zap,
      title: settings.language === 'ar' ? 'سريع وفعال' : 'Fast & Efficient',
      description: settings.language === 'ar'
        ? 'واجهة سريعة ومستجيبة مع تحديثات فورية'
        : 'Lightning-fast interface with real-time updates',
    },
  ];

  const documentTypes = [
    {
      icon: BookOpen,
      title: settings.language === 'ar' ? 'وثائق تقنية' : 'Technical Documentation',
      description: settings.language === 'ar'
        ? 'دليل المطورين، API، والوثائق التقنية'
        : 'Developer guides, APIs, and technical references',
    },
    {
      icon: Users,
      title: settings.language === 'ar' ? 'دليل المستخدم' : 'User Manuals',
      description: settings.language === 'ar'
        ? 'أدلة سهلة الفهم للمستخدمين النهائيين'
        : 'Easy-to-understand guides for end users',
    },
    {
      icon: Settings,
      title: settings.language === 'ar' ? 'دروس تفاعلية' : 'Interactive Tutorials',
      description: settings.language === 'ar'
        ? 'دروس خطوة بخطوة مع أمثلة عملية'
        : 'Step-by-step tutorials with practical examples',
    },
  ];

  return (
    <div className="min-h-full">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-ai-primary-50/20 py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              className="inline-flex items-center gap-2 bg-ai-primary-100 text-ai-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Sparkles className="w-4 h-4" />
              {settings.language === 'ar' ? 'مدعوم بالذكاء الاصطناعي' : 'AI-Powered'}
            </motion.div>
            
            <h1 className="text-5xl md:text-6xl font-bold mb-6 ai-gradient-text">
              {settings.language === 'ar' 
                ? 'بناء الوثائق بالذكاء الاصطناعي'
                : 'BuildDocWithAI'
              }
            </h1>
            
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
              {settings.language === 'ar'
                ? 'منصة ذكية لإنشاء وإدارة الوثائق الاحترافية باستخدام تقنيات الذكاء الاصطناعي المتقدمة من جميني'
                : 'Create intelligent, professional documentation with advanced AI technology powered by Gemini'
              }
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                size="lg" 
                className="ai-gradient-bg text-white hover:scale-105 transition-transform ai-button-glow"
              >
                {settings.language === 'ar' ? 'ابدأ الآن' : 'Get Started'}
                <ArrowRight className={`w-5 h-5 ${settings.rtl ? 'rotate-180' : ''}`} />
              </Button>
              
              <Button variant="outline" size="lg" className="hover:scale-105 transition-transform">
                {settings.language === 'ar' ? 'شاهد العرض التوضيحي' : 'View Demo'}
              </Button>
            </div>
          </motion.div>
        </div>
        
        {/* Background Animation */}
        <div className="absolute inset-0 -z-10">
          <motion.div
            className="absolute top-20 left-10 w-64 h-64 bg-ai-primary-200/30 rounded-full blur-3xl"
            animate={{
              x: [0, 100, 0],
              y: [0, -50, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            }}
          />
          <motion.div
            className="absolute bottom-20 right-10 w-96 h-96 bg-ai-accent-200/20 rounded-full blur-3xl"
            animate={{
              x: [0, -150, 0],
              y: [0, 100, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-card/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-4">
              {settings.language === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {settings.language === 'ar'
                ? 'اكتشف القوة الحقيقية لمنصة بناء الوثائق بالذكاء الاصطناعي'
                : 'Discover the power of intelligent documentation generation'
              }
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 ai-card-hover group">
                  <CardHeader className="text-center pb-4">
                    <div className="w-12 h-12 bg-ai-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-ai-primary-200 transition-colors">
                      <feature.icon className="w-6 h-6 text-ai-primary-600" />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Document Types Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-4">
              {settings.language === 'ar' ? 'أنواع الوثائق' : 'Document Types'}
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {settings.language === 'ar'
                ? 'إنشاء جميع أنواع الوثائق التي تحتاجها'
                : 'Generate all types of documentation you need'
              }
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {documentTypes.map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <Card className="text-center h-full hover:shadow-xl transition-all duration-300 border-2 hover:border-ai-primary-200">
                  <CardHeader>
                    <div className="w-16 h-16 bg-gradient-to-br from-ai-primary-100 to-ai-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <type.icon className="w-8 h-8 text-ai-primary-600" />
                    </div>
                    <CardTitle className="text-xl">{type.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base leading-relaxed">
                      {type.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-ai-primary-600 to-ai-primary-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-6">
              {settings.language === 'ar' 
                ? 'ابدأ رحلتك مع الذكاء الاصطناعي'
                : 'Start Your AI Documentation Journey'
              }
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              {settings.language === 'ar'
                ? 'انضم إلى آلاف المطورين والكتاب الذين يستخدمون منصتنا لإنشاء وثائق احترافية'
                : 'Join thousands of developers and writers using our platform to create professional documentation'
              }
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                variant="secondary"
                className="hover:scale-105 transition-transform text-ai-primary-700"
              >
                {settings.language === 'ar' ? 'ابدأ مجاناً' : 'Start Free'}
                <ArrowRight className={`w-5 h-5 ml-2 ${settings.rtl ? 'rotate-180 ml-0 mr-2' : ''}`} />
              </Button>
              
              <Button 
                size="lg" 
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-ai-primary-700 hover:scale-105 transition-all"
              >
                {settings.language === 'ar' ? 'تعلم المزيد' : 'Learn More'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Index;

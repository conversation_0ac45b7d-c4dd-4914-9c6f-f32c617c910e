
/**
 * Home Page Component
 * Landing page for the AI Documentation Platform
 */

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Header } from '@/components/Header';
import { useAuthStore } from '@/store/auth-store';
import { useAppStore } from '@/store/app-store';
import {
  FileText,
  Sparkles,
  Download,
  Globe,
  Zap,
  ArrowRight,
  BookOpen,
  Settings,
  Users
} from 'lucide-react';

const Index = () => {
  const { isAuthenticated } = useAuthStore();
  const { settings } = useAppStore();

  const features = [
    {
      icon: Sparkles,
      title: settings.language === 'ar' ? 'توليد ذكي بالذكاء الاصطناعي' : 'AI Content Generation',
      description: settings.language === 'ar'
        ? 'إنشاء خطط أعمال ودراسات جدوى احترافية بالذكاء الاصطناعي'
        : 'Create professional business plans and feasibility studies with AI',
    },
    {
      icon: BookOpen,
      title: settings.language === 'ar' ? 'مساعد كتابة ذكي' : 'Smart Writing Assistant',
      description: settings.language === 'ar'
        ? 'مساعد ذكي يوجهك خطوة بخطوة لإنشاء وثائق الأعمال'
        : 'Intelligent assistant guides you step-by-step through business document creation',
    },
    {
      icon: Download,
      title: settings.language === 'ar' ? 'تصدير PDF/Word عالي الجودة' : 'High-quality PDF/Word export',
      description: settings.language === 'ar'
        ? 'احصل على مستندات احترافية جاهزة للطباعة والمشاركة'
        : 'Get professional documents ready for printing and sharing',
    },
    {
      icon: Settings,
      title: settings.language === 'ar' ? 'قوالب احترافية جاهزة' : 'Ready-made professional templates',
      description: settings.language === 'ar'
        ? 'قوالب معدة مسبقاً لجميع أنواع وثائق الأعمال السعودية'
        : 'Pre-built templates for all types of Saudi business documents',
    },
    {
      icon: Globe,
      title: settings.language === 'ar' ? 'دعم كامل للعربية والإنجليزية' : 'Full Arabic/English language support',
      description: settings.language === 'ar'
        ? 'واجهة RTL كاملة مع دعم الخطوط العربية الاحترافية'
        : 'Complete RTL interface with professional Arabic typography',
    },
    {
      icon: Zap,
      title: settings.language === 'ar' ? 'أسعار مرنة (طبقة مجانية)' : 'Flexible pricing (free tier available)',
      description: settings.language === 'ar'
        ? 'ابدأ مجاناً مع إمكانية الترقية للميزات المتقدمة'
        : 'Start free with option to upgrade for advanced features',
    },
  ];

  const documentTypes = [
    {
      icon: FileText,
      title: settings.language === 'ar' ? 'خطة العمل' : 'Business Plan',
      description: settings.language === 'ar'
        ? 'خطط أعمال شاملة مع التحليل المالي ودراسة السوق'
        : 'Comprehensive business plans with financial analysis and market research',
      badge: settings.language === 'ar' ? 'الأكثر طلباً' : 'Most Popular',
    },
    {
      icon: BookOpen,
      title: settings.language === 'ar' ? 'دراسة الجدوى' : 'Feasibility Study',
      description: settings.language === 'ar'
        ? 'دراسات جدوى تفصيلية للمشاريع والاستثمارات'
        : 'Detailed feasibility studies for projects and investments',
      badge: settings.language === 'ar' ? 'احترافي' : 'Professional',
    },
    {
      icon: Users,
      title: settings.language === 'ar' ? 'عرض تقديمي للمستثمرين' : 'Pitch Deck',
      description: settings.language === 'ar'
        ? 'عروض تقديمية مقنعة لجذب المستثمرين والشركاء'
        : 'Compelling presentations to attract investors and partners',
      badge: settings.language === 'ar' ? 'مميز' : 'Premium',
    },
    {
      icon: Settings,
      title: settings.language === 'ar' ? 'تقرير الاستثمار' : 'Investment Report',
      description: settings.language === 'ar'
        ? 'تقارير استثمارية مفصلة مع التوصيات والتحليلات'
        : 'Detailed investment reports with recommendations and analysis',
      badge: settings.language === 'ar' ? 'جديد' : 'New',
    },
  ];

  return (
    <div className="min-h-full">
      <Header />

      {/* Hero Section */}
      <section id="hero" className="relative overflow-hidden bg-gradient-to-br from-background via-background to-saudi-emerald-50/20 pt-24 pb-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              className="inline-flex items-center gap-2 bg-saudi-emerald-100 text-saudi-emerald-700 px-4 py-2 rounded-full text-sm font-medium mb-6"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Sparkles className="w-4 h-4" />
              {settings.language === 'ar' ? 'مدعوم بالذكاء الاصطناعي' : 'AI-Powered'}
            </motion.div>

            <h1 className="text-5xl md:text-6xl font-bold mb-6 ai-gradient-text font-vazirmatn">
              {settings.language === 'ar'
                ? 'بناء الوثائق بالذكاء الاصطناعي'
                : 'BuildDocwithai'
              }
            </h1>

            <p className="text-xl text-muted-foreground mb-4 max-w-3xl mx-auto leading-relaxed">
              {settings.language === 'ar'
                ? 'حوّل أفكارك إلى وثائق أعمال احترافية بالذكاء الاصطناعي'
                : 'Turn your ideas into professional documents with AI'
              }
            </p>

            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              {settings.language === 'ar'
                ? 'أنشئ خطط الأعمال ودراسات الجدوى والعروض التقديمية في دقائق'
                : 'Create business plans, feasibility studies, and presentations in minutes'
              }
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="ai-gradient-bg text-white hover:scale-105 transition-transform ai-button-glow px-8 py-3"
              >
                {settings.language === 'ar' ? 'ابدأ الآن' : 'Get Started Now'}
                <ArrowRight className={`w-5 h-5 ml-2 ${settings.rtl ? 'rotate-180 ml-0 mr-2' : ''}`} />
              </Button>

              <Button variant="outline" size="lg" className="hover:scale-105 transition-transform px-8 py-3">
                {settings.language === 'ar' ? 'جرب العرض التوضيحي' : 'Try AI Demo'}
              </Button>
            </div>
          </motion.div>
        </div>
        
        {/* Background Animation */}
        <div className="absolute inset-0 -z-10">
          <motion.div
            className="absolute top-20 left-10 w-64 h-64 bg-ai-primary-200/30 rounded-full blur-3xl"
            animate={{
              x: [0, 100, 0],
              y: [0, -50, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear",
            }}
          />
          <motion.div
            className="absolute bottom-20 right-10 w-96 h-96 bg-ai-accent-200/20 rounded-full blur-3xl"
            animate={{
              x: [0, -150, 0],
              y: [0, 100, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear",
            }}
          />
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-card/30">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-4 font-vazirmatn">
              {settings.language === 'ar' ? 'المميزات الرئيسية' : 'Key Features'}
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {settings.language === 'ar'
                ? 'اكتشف قوة منصة BuildDocwithai في إنشاء وثائق الأعمال الاحترافية'
                : 'Discover the power of BuildDocwithai for creating professional business documents'
              }
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 ai-card-hover group border-2 hover:border-saudi-emerald-200">
                  <CardHeader className="text-center pb-4">
                    <div className="w-12 h-12 bg-saudi-emerald-100 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-saudi-emerald-200 transition-colors">
                      <feature.icon className="w-6 h-6 text-saudi-emerald-600" />
                    </div>
                    <CardTitle className="text-lg font-vazirmatn">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Templates Section */}
      <section id="templates" className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold mb-4 font-vazirmatn">
              {settings.language === 'ar' ? 'القوالب المتاحة' : 'Available Templates'}
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {settings.language === 'ar'
                ? 'اختر من مجموعة واسعة من قوالب وثائق الأعمال المصممة خصيصاً للسوق السعودي'
                : 'Choose from a wide range of business document templates designed specifically for the Saudi market'
              }
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {documentTypes.map((type, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center h-full hover:shadow-xl transition-all duration-300 border-2 hover:border-saudi-blue-200 group relative overflow-hidden">
                  <CardHeader className="relative">
                    {type.badge && (
                      <div className="absolute top-4 right-4 bg-saudi-emerald-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        {type.badge}
                      </div>
                    )}
                    <div className="w-16 h-16 bg-gradient-to-br from-saudi-blue-100 to-saudi-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:from-saudi-blue-200 group-hover:to-saudi-blue-300 transition-all">
                      <type.icon className="w-8 h-8 text-saudi-blue-600" />
                    </div>
                    <CardTitle className="text-xl font-vazirmatn">{type.title}</CardTitle>
                  </CardHeader>
                  <CardContent className="pb-6">
                    <CardDescription className="text-base leading-relaxed mb-4">
                      {type.description}
                    </CardDescription>
                    <Button
                      variant="outline"
                      size="sm"
                      className="hover:bg-saudi-blue-50 hover:border-saudi-blue-300 transition-colors"
                    >
                      {settings.language === 'ar' ? 'معاينة' : 'Preview'}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-saudi-navy-800 to-saudi-blue-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold mb-6 font-vazirmatn">
              {settings.language === 'ar'
                ? 'ابدأ إنشاء وثائق احترافية اليوم'
                : 'Start creating professional documents today'
              }
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-3xl mx-auto">
              {settings.language === 'ar'
                ? 'انضم إلى آلاف رواد الأعمال السعوديين الذين يستخدمون BuildDocwithai لإنشاء وثائق أعمال احترافية'
                : 'Join thousands of Saudi entrepreneurs using BuildDocwithai to create professional business documents'
              }
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="secondary"
                className="hover:scale-105 transition-transform text-saudi-navy-700 px-8 py-3"
              >
                {settings.language === 'ar' ? 'ابدأ التوليد' : 'Start Generating'}
                <ArrowRight className={`w-5 h-5 ml-2 ${settings.rtl ? 'rotate-180 ml-0 mr-2' : ''}`} />
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-saudi-navy-700 hover:scale-105 transition-all px-8 py-3"
              >
                {settings.language === 'ar' ? 'تعلم المزيد' : 'Learn More'}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Index;

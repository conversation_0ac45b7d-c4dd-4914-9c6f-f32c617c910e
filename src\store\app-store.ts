
/**
 * Application Store using Zustand
 * Manages global application state and settings
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface Document {
  id: string;
  title: string;
  content: string;
  type: 'technical' | 'user' | 'api' | 'tutorial';
  language: 'en' | 'ar';
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
}

interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'ar';
  rtl: boolean;
  geminiApiKey: string | null;
  autoSave: boolean;
  defaultDocumentType: 'technical' | 'user' | 'api' | 'tutorial';
  notifications: boolean;
}

interface AppState {
  // Documents
  documents: Document[];
  currentDocument: Document | null;
  
  // UI State
  sidebarOpen: boolean;
  isGenerating: boolean;
  
  // Settings
  settings: AppSettings;
  
  // Loading states
  loading: {
    documents: boolean;
    generation: boolean;
    export: boolean;
  };
}

interface AppActions {
  // Document management
  addDocument: (document: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateDocument: (id: string, updates: Partial<Document>) => void;
  deleteDocument: (id: string) => void;
  setCurrentDocument: (document: Document | null) => void;
  
  // UI actions
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setGenerating: (generating: boolean) => void;
  
  // Settings
  updateSettings: (settings: Partial<AppSettings>) => void;
  setGeminiApiKey: (apiKey: string) => void;
  setLanguage: (language: 'en' | 'ar') => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  
  // Loading states
  setLoading: (key: keyof AppState['loading'], loading: boolean) => void;
  
  // Utility functions
  getDocumentById: (id: string) => Document | undefined;
  getDocumentsByType: (type: Document['type']) => Document[];
  searchDocuments: (query: string) => Document[];
}

type AppStore = AppState & AppActions;

const defaultSettings: AppSettings = {
  theme: 'system',
  language: 'en',
  rtl: false,
  geminiApiKey: null,
  autoSave: true,
  defaultDocumentType: 'technical',
  notifications: true,
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // Initial state
      documents: [],
      currentDocument: null,
      sidebarOpen: true,
      isGenerating: false,
      settings: defaultSettings,
      loading: {
        documents: false,
        generation: false,
        export: false,
      },

      // Document management
      addDocument: (documentData) => {
        const newDocument: Document = {
          ...documentData,
          id: crypto.randomUUID(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        
        set((state) => ({
          documents: [...state.documents, newDocument],
        }));
      },

      updateDocument: (id, updates) => {
        set((state) => ({
          documents: state.documents.map((doc) =>
            doc.id === id
              ? { ...doc, ...updates, updatedAt: new Date() }
              : doc
          ),
          currentDocument:
            state.currentDocument?.id === id
              ? { ...state.currentDocument, ...updates, updatedAt: new Date() }
              : state.currentDocument,
        }));
      },

      deleteDocument: (id) => {
        set((state) => ({
          documents: state.documents.filter((doc) => doc.id !== id),
          currentDocument:
            state.currentDocument?.id === id ? null : state.currentDocument,
        }));
      },

      setCurrentDocument: (document) => {
        set({ currentDocument: document });
      },

      // UI actions
      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }));
      },

      setSidebarOpen: (open) => {
        set({ sidebarOpen: open });
      },

      setGenerating: (generating) => {
        set({ isGenerating: generating });
      },

      // Settings
      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        }));
      },

      setGeminiApiKey: (apiKey) => {
        set((state) => ({
          settings: { ...state.settings, geminiApiKey: apiKey },
        }));
      },

      setLanguage: (language) => {
        const rtl = language === 'ar';
        set((state) => ({
          settings: { ...state.settings, language, rtl },
        }));
        
        // Update document direction
        document.documentElement.dir = rtl ? 'rtl' : 'ltr';
        document.documentElement.lang = language;
      },

      setTheme: (theme) => {
        set((state) => ({
          settings: { ...state.settings, theme },
        }));
        
        // Apply theme to document
        const root = document.documentElement;
        if (theme === 'dark') {
          root.classList.add('dark');
        } else if (theme === 'light') {
          root.classList.remove('dark');
        } else {
          // System theme
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          root.classList.toggle('dark', prefersDark);
        }
      },

      // Loading states
      setLoading: (key, loading) => {
        set((state) => ({
          loading: { ...state.loading, [key]: loading },
        }));
      },

      // Utility functions
      getDocumentById: (id) => {
        return get().documents.find((doc) => doc.id === id);
      },

      getDocumentsByType: (type) => {
        return get().documents.filter((doc) => doc.type === type);
      },

      searchDocuments: (query) => {
        const { documents } = get();
        const lowercaseQuery = query.toLowerCase();
        
        return documents.filter(
          (doc) =>
            doc.title.toLowerCase().includes(lowercaseQuery) ||
            doc.content.toLowerCase().includes(lowercaseQuery) ||
            doc.tags.some((tag) => tag.toLowerCase().includes(lowercaseQuery))
        );
      },
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        documents: state.documents,
        settings: state.settings,
        sidebarOpen: state.sidebarOpen,
      }),
    }
  )
);

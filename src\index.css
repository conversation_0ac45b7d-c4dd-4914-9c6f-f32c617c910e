
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts for better typography - Saudi-inspired professional fonts */
@import url('https://fonts.googleapis.com/css2?family=Vazirmatn:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Design system definition for AI Documentation Platform */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 26% 14%;

    --card: 0 0% 100%;
    --card-foreground: 220 26% 14%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 26% 14%;

    /* Saudi-inspired professional colors - Navy Blue primary */
    --primary: 220 91% 20%;
    --primary-foreground: 0 0% 98%;

    /* Royal Blue secondary */
    --secondary: 217 91% 60%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 8.9% 46.1%;

    /* Emerald accent for success/trust */
    --accent: 160 84% 39%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 91% 20%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* BuildDocwithai Saudi-inspired gradients */
    --ai-gradient-primary: linear-gradient(135deg, hsl(220 91% 20%) 0%, hsl(217 91% 60%) 100%);
    --ai-gradient-secondary: linear-gradient(135deg, hsl(160 84% 39%) 0%, hsl(142 76% 36%) 100%);
    --ai-gradient-success: linear-gradient(135deg, hsl(160 84% 39%) 0%, hsl(160 84% 49%) 100%);
    --ai-gradient-accent: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(160 84% 39%) 100%);
  }

  .dark {
    --background: 220 26% 4%;
    --foreground: 0 0% 98%;

    --card: 220 26% 6%;
    --card-foreground: 0 0% 98%;

    --popover: 220 26% 6%;
    --popover-foreground: 0 0% 98%;

    /* Dark mode Saudi colors */
    --primary: 217 91% 60%;
    --primary-foreground: 220 26% 4%;

    --secondary: 220 26% 14%;
    --secondary-foreground: 0 0% 98%;

    --muted: 220 26% 14%;
    --muted-foreground: 220 8.9% 65.1%;

    --accent: 160 84% 49%;
    --accent-foreground: 220 26% 4%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 26% 14%;
    --input: 220 26% 14%;
    --ring: 217 91% 60%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Dark mode BuildDocwithai gradients */
    --ai-gradient-primary: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(160 84% 49%) 100%);
    --ai-gradient-secondary: linear-gradient(135deg, hsl(160 84% 49%) 0%, hsl(142 76% 46%) 100%);
    --ai-gradient-success: linear-gradient(135deg, hsl(160 84% 49%) 0%, hsl(160 84% 59%) 100%);
    --ai-gradient-accent: linear-gradient(135deg, hsl(217 91% 60%) 0%, hsl(160 84% 49%) 100%);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, sans-serif;
  }

  /* RTL Support with Vazirmatn font */
  [dir="rtl"] {
    direction: rtl;
    font-family: 'Vazirmatn', 'Noto Sans Arabic', system-ui, sans-serif;
  }

  [dir="rtl"] .font-arabic,
  .font-vazirmatn {
    font-family: 'Vazirmatn', 'Noto Sans Arabic', system-ui, sans-serif;
  }

  /* English content in RTL layout */
  [dir="rtl"] .font-inter {
    font-family: 'Inter', system-ui, sans-serif;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/20;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

@layer components {
  /* AI Platform specific components */
  .ai-gradient-text {
    background: var(--ai-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .ai-gradient-bg {
    background: var(--ai-gradient-primary);
  }

  .ai-card-hover {
    @apply transition-all duration-300 hover:scale-[1.02] hover:shadow-lg;
  }

  .ai-button-glow {
    @apply relative overflow-hidden;
  }

  .ai-button-glow::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  .ai-button-glow:hover::before {
    transform: translateX(100%);
  }

  .ai-typing-cursor::after {
    content: '|';
    @apply animate-pulse;
  }

  /* Loading states */
  .ai-loading-dots::after {
    content: '';
    @apply inline-block w-4 h-4 rounded-full bg-current opacity-60 animate-pulse;
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .rtl-support {
    @apply [dir="rtl"]:text-right [dir="rtl"]:flex-row-reverse;
  }

  .writing-vertical {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }
}

/* Animation utilities */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }
  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(.9, .9, .9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(.97, .97, .97);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

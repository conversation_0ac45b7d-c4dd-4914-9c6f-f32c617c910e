
/**
 * Authentication Store using Zustand
 * Manages user authentication state and related actions
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User, Session } from '@supabase/supabase-js';
import { supabase, signIn, signUp, signOut } from '@/lib/supabase';

interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  initialized: boolean;
}

interface AuthActions {
  // Authentication actions
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string, metadata?: any) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  
  // State management
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
  
  // Utility functions
  isAuthenticated: () => boolean;
  getUserRole: () => string | null;
  getUserMetadata: () => any;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      session: null,
      loading: true,
      initialized: false,

      // Authentication actions
      login: async (email: string, password: string) => {
        set({ loading: true });
        
        try {
          const { data, error } = await signIn(email, password);
          
          if (error) {
            set({ loading: false });
            return { success: false, error: error.message };
          }
          
          if (data.user && data.session) {
            set({ 
              user: data.user, 
              session: data.session, 
              loading: false 
            });
            return { success: true };
          }
          
          set({ loading: false });
          return { success: false, error: 'Login failed' };
        } catch (error) {
          set({ loading: false });
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Login failed' 
          };
        }
      },

      register: async (email: string, password: string, metadata?: any) => {
        set({ loading: true });
        
        try {
          const { data, error } = await signUp(email, password, metadata);
          
          if (error) {
            set({ loading: false });
            return { success: false, error: error.message };
          }
          
          // Note: With email confirmation, user might be null initially
          if (data.user) {
            set({ 
              user: data.user, 
              session: data.session, 
              loading: false 
            });
          } else {
            set({ loading: false });
          }
          
          return { success: true };
        } catch (error) {
          set({ loading: false });
          return { 
            success: false, 
            error: error instanceof Error ? error.message : 'Registration failed' 
          };
        }
      },

      logout: async () => {
        set({ loading: true });
        
        try {
          await signOut();
          set({ 
            user: null, 
            session: null, 
            loading: false 
          });
        } catch (error) {
          console.error('Logout error:', error);
          // Force logout on client side even if server logout fails
          set({ 
            user: null, 
            session: null, 
            loading: false 
          });
        }
      },

      // State management
      setUser: (user) => set({ user }),
      setSession: (session) => set({ session, user: session?.user || null }),
      setLoading: (loading) => set({ loading }),

      initialize: async () => {
        if (get().initialized) return;
        
        set({ loading: true });
        
        try {
          // Set up auth state listener
          const { data: { subscription } } = supabase.auth.onAuthStateChange(
            (event, session) => {
              console.log('Auth state changed:', event, session?.user?.email);
              
              set({ 
                session, 
                user: session?.user || null,
                loading: false,
                initialized: true,
              });
            }
          );

          // Get initial session
          const { data: { session } } = await supabase.auth.getSession();
          
          set({ 
            session, 
            user: session?.user || null,
            loading: false,
            initialized: true,
          });

          // Store subscription for cleanup
          return () => subscription.unsubscribe();
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({ 
            loading: false, 
            initialized: true 
          });
        }
      },

      // Utility functions
      isAuthenticated: () => {
        const { user, session } = get();
        return !!(user && session);
      },

      getUserRole: () => {
        const { user } = get();
        return user?.user_metadata?.role || user?.app_metadata?.role || null;
      },

      getUserMetadata: () => {
        const { user } = get();
        return user?.user_metadata || {};
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ 
        user: state.user, 
        session: state.session,
        initialized: state.initialized,
      }),
    }
  )
);

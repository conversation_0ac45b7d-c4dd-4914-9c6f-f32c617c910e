// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zkjasrxgkkhfcafwilsb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpramFzcnhna2toZmNhZndpbHNiIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjE0MjIsImV4cCI6MjA2Nzc5NzQyMn0.XljfQR5z8rOqeTplTQ78llsYYeind8jMw3Nl1HqnrvQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});